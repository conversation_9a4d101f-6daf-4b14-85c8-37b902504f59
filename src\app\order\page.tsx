import Header from '@/components/Header';
import Footer from '@/components/Footer';
import OrderForm from '@/components/OrderForm';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function OrderPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 bg-secondary py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl">
            <div className="text-center mb-8">
              <h1 className="font-headline text-3xl font-bold tracking-tight md:text-4xl">Place Your Order</h1>
              <p className="mt-2 text-muted-foreground">Fill out the form below and we'll get back to you shortly.</p>
            </div>
            <OrderForm />
            <div className="mt-8 text-center">
              <Button variant="link" asChild>
                <Link href="/">Back to Home</Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
