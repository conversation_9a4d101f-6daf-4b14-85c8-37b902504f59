@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-body), sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-headline), sans-serif;
}

@layer base {
  :root {
    --background: 40 33% 98%;
    --foreground: 34 20% 15%;
    --card: 0 0% 100%;
    --card-foreground: 34 20% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 34 20% 15%;
    --primary: 34 43% 70%;
    --primary-foreground: 34 20% 15%;
    --secondary: 60 56% 91%;
    --secondary-foreground: 34 20% 15%;
    --muted: 60 56% 91%;
    --muted-foreground: 34 20% 40%;
    --accent: 34 43% 75%;
    --accent-foreground: 34 20% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 60 56% 88%;
    --input: 60 56% 88%;
    --ring: 34 43% 70%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 34 10% 10%;
    --foreground: 40 33% 98%;
    --card: 34 10% 12%;
    --card-foreground: 40 33% 98%;
    --popover: 34 10% 10%;
    --popover-foreground: 40 33% 98%;
    --primary: 34 43% 70%;
    --primary-foreground: 34 10% 10%;
    --secondary: 34 10% 20%;
    --secondary-foreground: 40 33% 98%;
    --muted: 34 10% 20%;
    --muted-foreground: 40 33% 80%;
    --accent: 60 56% 91%;
    --accent-foreground: 34 10% 10%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 34 10% 20%;
    --input: 34 10% 20%;
    --ring: 34 43% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .fade-in {
    animation: fadeIn 1s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
