import Link from 'next/link';
import { Fish } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  return (
    <footer id="contact" className="border-t bg-secondary">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left">
          <div>
            <Link href="/" className="flex items-center justify-center md:justify-start gap-2 font-bold mb-2">
              <Fish className="h-6 w-6 text-primary" />
              <span className="font-headline text-lg">Coastal Catch</span>
            </Link>
            <p className="text-sm text-muted-foreground">Your source for premium quality dry fish.</p>
          </div>
          <div>
            <h3 className="font-headline font-semibold mb-2">Contact Us</h3>
            <p className="text-sm text-muted-foreground">123 Fishery Lane, Coastal Town, 12345</p>
            <p className="text-sm text-muted-foreground">Email: <EMAIL></p>
            <p className="text-sm text-muted-foreground">Phone: +91 12345 67890</p>
          </div>
           <div>
            <h3 className="font-headline font-semibold mb-2">Quick Links</h3>
             <ul className="text-sm text-muted-foreground space-y-1">
                <li><Link href="/#products" className="hover:text-primary">Products</Link></li>
                <li><Link href="/order" className="hover:text-primary">Order</Link></li>
                <li><Link href="/login" className="hover:text-primary">Login</Link></li>
             </ul>
          </div>
        </div>
        <div className="mt-8 border-t pt-4 text-center text-sm text-muted-foreground">
          <p>&copy; {currentYear} Coastal Catch. All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  );
}
