# **App Name**: Coastal Catch

## Core Features:

- Product Showcase: Display a product grid showcasing each of the 5 dry fish products with images, names, and short descriptions.
- Order Placement: Implement a user-friendly order form with fields for name, mobile number, delivery address, product selection (dropdown), quantity, and payment confirmation (yes/no).
- User Authentication: Create a basic login page with branding, authentication message, and links to logout/home.
- Enhancements: Incorporate smooth hover animations on product cards, a back-to-top button, and scroll reveal/fade-in effects for sections.
- Responsiveness: Ensure the website is mobile-responsive using CSS3 and Tailwind CSS.

## Style Guidelines:

- Primary color: Light beige (#F5F5DC) to evoke a sense of natural simplicity and authenticity, suitable for a local-market feel.
- Background color: Off-white (#FAF9F6) for a clean and unobtrusive backdrop.
- Accent color: Pale brown (#D2B48C), used sparingly for highlighting calls to action, such as the 'Order Now' button; slightly darker than the background to create good contrast without being overwhelming.
- Font pairing: 'Poppins' (sans-serif) for headlines and short text, and 'PT Sans' (sans-serif) for body text. Note: currently only Google Fonts are supported.
- Use simple, hand-drawn style icons to enhance the local-market feel, keeping the aesthetic consistent throughout.
- Employ a grid-based layout for the product display, with soft shadows and rounded cards to create a modern, inviting look.
- Incorporate subtle hover animations on product cards to provide a tactile, engaging user experience.