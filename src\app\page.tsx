import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { products } from '@/lib/constants';
import { ProductCard } from '@/components/ProductCard';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WhatsAppButton from '@/components/WhatsAppButton';
import BackToTopButton from '@/components/BackToTopButton';
import Link from 'next/link';

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <section className="relative h-[60vh] min-h-[400px] w-full">
           <Image
            src="https://placehold.co/1920x1080.png"
            alt="Fishermen at sea"
            data-ai-hint="fishing boat"
            layout="fill"
            objectFit="cover"
            className="z-0"
          />
          <div className="absolute inset-0 bg-black/50" />
          <div className="relative z-10 flex h-full flex-col items-center justify-center text-center text-white fade-in">
            <h1 className="font-headline text-4xl font-bold tracking-tight md:text-6xl">
              Coastal Catch
            </h1>
            <p className="mt-4 max-w-2xl text-lg text-gray-200">
              Authentic Dry Fish, Straight from the Coast to Your Kitchen.
            </p>
            <Button asChild size="lg" className="mt-8 bg-accent text-accent-foreground hover:bg-accent/90">
              <Link href="/order">Order Now</Link>
            </Button>
          </div>
        </section>

        <section id="products" className="py-12 md:py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12 fade-in">
              <h2 className="font-headline text-3xl font-bold tracking-tight md:text-4xl">
                Our Products
              </h2>
              <p className="mt-3 text-lg text-muted-foreground">
                Freshly sourced and naturally dried to perfection.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 fade-in">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        </section>

        <section className="bg-secondary py-12 md:py-20">
            <div className="container mx-auto px-4 text-center fade-in">
                 <h2 className="font-headline text-3xl font-bold tracking-tight md:text-4xl">
                    Ready to Taste the Tradition?
                </h2>
                <p className="mt-3 text-lg text-muted-foreground">
                    Place your order today and bring home the authentic flavors of the coast.
                </p>
                <Button asChild size="lg" className="mt-8 bg-accent text-accent-foreground hover:bg-accent/90">
                  <Link href="/order">Order Now</Link>
                </Button>
            </div>
        </section>

      </main>
      <Footer />
      <WhatsAppButton phoneNumber="911234567890" />
      <BackToTopButton />
    </div>
  );
}
