import type { Product } from './types';

export const products: Product[] = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    description: 'Traditional flavor, perfect for curries and fries.',
    image: 'https://placehold.co/400x300.png',
    imageHint: 'dried fish',
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON>',
    description: 'Crispy and savory, an ideal snack or side dish.',
    image: 'https://placehold.co/400x300.png',
    imageHint: 'anchovies dried',
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    description: 'Rich in taste, great for flavorful gravies.',
    image: 'https://placehold.co/400x300.png',
    imageHint: 'ribbonfish dried',
  },
  {
    id: 4,
    name: 'Goa Netthili',
    description: 'A coastal specialty with a unique tangy twist.',
    image: 'https://placehold.co/400x300.png',
    imageHint: 'spicy anchovies',
  },
  {
    id: 5,
    name: '<PERSON><PERSON>',
    description: 'A rare delicacy known for its distinct aroma.',
    image: 'https://placehold.co/400x300.png',
    imageHint: 'dried seafood',
  },
];
